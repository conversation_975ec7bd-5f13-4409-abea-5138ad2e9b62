package com.smartcar.easylauncher.home.relax;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Bundle;
import android.os.VibrationEffect;
import android.os.Vibrator;
import com.smartcar.easylauncher.shared.utils.haptics.HapticUtils;
import android.util.SparseIntArray;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.DecelerateInterpolator;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.bytedance.scene.Scene;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.databinding.FragmentWoodeFishMapBinding;
import com.smartcar.easylauncher.core.manager.DataManager;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;

/**
 * 功德鱼
 *
 * <AUTHOR>
 */
public class MapWoodenFishScene extends Scene {
    public static final String TAG = MapWoodenFishScene.class.getSimpleName();
    private static AnimatorSet animatorSet;
    private final SparseIntArray knockWoodenFishVoiceIds = new SparseIntArray();
    private FragmentWoodeFishMapBinding binding;
    private int tab = 0;
    private SoundPool soundPool;
    // 文字大小常量（dp值） - 针对车机屏幕优化
    private static final float TEXT_SIZE_DP = 28;        // 增大普通文字，提升可读性
    private static final float LARGE_TEXT_SIZE_DP = 38;  // 增大特效文字，增强视觉冲击

    // 缓存文字大小（dp值，让AndroidAutoSize自动适配）
    private int normalTextSizeDp;
    private int largeTextSizeDp;
    private static final int MAX_ACTIVE_ANIMATIONS = 4;  // 增加同时显示的动画数量
    private int lastPosition = 0;
    /**
     * 连击计数
     */
    private int comboCount = 0;
    /**
     * 上次点击时间戳
     */
    private long lastClickTimestamp = 0;
    /**
     * 优化连击判定时间窗口 - 提升用户体验
     */
    private static final long COMBO_WINDOW = 400;        // 延长连击窗口，更容易触发
    /**
     * 调整特效触发概率 - 增加趣味性
     */
    private static final float SPECIAL_CHANCE = 0.15f;   // 略微提高特效概率

    /**
     * 普通文字 - 增加更多变化
     */
    private final String[] MERIT_TEXTS = new String[]{
            "+1",
            "功德+1",
            "善哉",
            "阿弥陀佛",
            "南无",
            "功德无量"
    };

    /**
     * 特殊文字 - 增加更多佛教元素
     */
    private final String[] SPECIAL_TEXTS = new String[]{
            "功德满满",
            "佛法无边",
            "心诚则灵",
            "大慈大悲",
            "普度众生",
            "福慧双修",
            "六时吉祥",
            "法喜充满"
    };

    /**
     * 连击文字 - 优化连击体验
     */
    private final String[] COMBO_TEXTS = new String[]{
            "双倍功德",
            "三倍功德",
            "功德暴击",
            "功德MAX",
            "无量功德",
            "圆满功德"
    };

    /**
     * 添加对象池相关字段
     */
    private static final int POOL_SIZE = 15;
    private final Queue<TextView> textViewPool = new LinkedList<>();
    private final List<TextView> activeAnimationViews = new ArrayList<>();

    /**
     * 预缓存动画插值器
     */
    private final DecelerateInterpolator normalInterpolator = new DecelerateInterpolator(1.1f);

    /**
     * 预缓存颜色值
     * 金色
     * 橙色
     * 粉色
     */
    private static final int COLOR_NORMAL = 0xFFE6B422;
    private static final int COLOR_COMBO = 0xFFFF9800;
    private static final int COLOR_SPECIAL = 0xFFE91E63;
    /**
     * 连击阈值，超过该次数触发特殊动画和文字效果 - 降低阈值提升体验
     */
    private static final int COMBO_THRESHOLD = 6;  // 降低连击阈值，更容易触发特效




    @NonNull
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @NonNull ViewGroup container, Bundle savedInstanceState) {
        binding = FragmentWoodeFishMapBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView();
    }

    private void initView() {
        // 在AndroidAutoSize项目中，直接使用dp值，让AutoSize自动处理屏幕适配
        // 不需要手动转换为px，避免与AutoSize的自动缩放机制冲突
        normalTextSizeDp = (int) TEXT_SIZE_DP;
        largeTextSizeDp = (int) LARGE_TEXT_SIZE_DP;

        tab = DataManager.getPower();
        binding.meritsCount.setText(getString(R.string.merits_count, tab));
        knockWoodenFishVoiceIds.append(R.raw.knock_wooden_fish, 0);
        knockWoodenFishVoiceIds.append(R.raw.knock_wooden_fish_2, 0);
        animatorSet = new AnimatorSet();
        soundPool = new SoundPool(knockWoodenFishVoiceIds.size(), AudioManager.STREAM_MUSIC, 0);

        binding.rlWxbg.setOnClickListener(v -> {
            // 木鱼动画
            startScaleOutAnimD(getActivity(), binding.dOnclick);
            startScaleInAnimD(getActivity(), binding.dOnclick);

            // 创建功德文字并播放动画
            showMeritAnimation();

            // 更新功德值
            tab = tab + 1;
            DataManager.setPower(tab);
            binding.meritsCount.setText(getString(R.string.merits_count, tab));

            // 播放敲击音效
            playKnockVoice(R.raw.knock_wooden_fish);

            // 优化震动反馈 - 根据连击状态调整震动强度
            performHapticFeedback();
        });

        // 在 initView() 中初始化对象池
        initTextViewPool();
    }

    private void initTextViewPool() {
        for (int i = 0; i < POOL_SIZE; i++) {
            TextView textView = new TextView(requireActivity());
            // 使用更灵活的LayoutParams，确保文字有足够空间显示
            textView.setLayoutParams(new ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT));
            // 在AndroidAutoSize项目中使用dp单位，让AutoSize自动处理屏幕适配
            textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, normalTextSizeDp);
            textView.setLayerType(View.LAYER_TYPE_HARDWARE, null);
            // 预设一些属性以减少动态分配
            textView.setPivotX(0);
            textView.setPivotY(0);
            // 减少文字padding，提高显示效果
            textView.setIncludeFontPadding(false);
            // 设置字体样式，增强视觉效果
            textView.setTypeface(null, android.graphics.Typeface.BOLD);
            // 确保文字不会被截断
            textView.setSingleLine(true);
            textView.setEllipsize(null); // 不使用省略号
            // 设置足够的内边距，确保文字完整显示
            int padding = 8; // dp值，AutoSize会自动转换
            textView.setPadding(padding, padding, padding, padding);
            textViewPool.offer(textView);
        }
    }

    private void playKnockVoice(int resId) {
        if (soundPool == null) {
            Toast.makeText(getActivity(), "Application not init", Toast.LENGTH_SHORT).show();
            return;
        } else if (resId <= 0) {
            Toast.makeText(getActivity(), "Resource Error", Toast.LENGTH_SHORT).show();
            return;
        } else if (knockWoodenFishVoiceIds.get(resId) == 0) {
            soundPool.load(getActivity(), resId, 1);
            soundPool.setOnLoadCompleteListener((soundPool, sampleId, status) -> {
                if (status == 0) {
                    knockWoodenFishVoiceIds.put(resId, sampleId);
                    soundPool.play(knockWoodenFishVoiceIds.get(resId), 1f, 1f, 1, 0, 1f);
                }
            });
            return;
        }
        soundPool.play(knockWoodenFishVoiceIds.get(resId), 1f, 1f, 1, 0, 1f);
    }

    /**
     * 优化的震动反馈
     */
    private void performHapticFeedback() {
        // 统一使用 HapticUtils，内部根据系统触感与幅度能力做降级
        HapticUtils.vibrateAdaptive(getContext(), comboCount, COMBO_THRESHOLD);
    }

    public static void startScaleInAnimD(Context context, View view) {
        Animation animation = AnimationUtils.loadAnimation(context, R.anim.inanimation);
        if (view != null) {
            view.startAnimation(animation);
        }
    }

    public static void startScaleOutAnimD(Context context, View view) {
        Animation animation = AnimationUtils.loadAnimation(context, R.anim.ouanimation);
        if (view != null) {
            view.startAnimation(animation);
        }
    }



    private void showMeritAnimation() {
        if (activeAnimationViews.size() >= MAX_ACTIVE_ANIMATIONS) {
            return;
        }

        // 检查连击
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastClickTimestamp < COMBO_WINDOW) {
            comboCount++;
        } else {
            comboCount = 0;
        }
        lastClickTimestamp = currentTime;

        final TextView meritText = textViewPool.poll();
        if (meritText == null) {
            return; // 如果没有可用的TextView，直接返回
        }

        // 重置状态
        meritText.setAlpha(0f);
        meritText.setScaleX(1f);
        meritText.setScaleY(1f);
        meritText.setShadowLayer(0, 0, 0, 0);

        // 确定显示类型和样式
        final boolean isSpecialEffect;
        if (comboCount >= COMBO_THRESHOLD) {
            setupComboText(meritText);
            isSpecialEffect = true;
        } else if (Math.random() < SPECIAL_CHANCE) {
            setupSpecialText(meritText);
            isSpecialEffect = true;
        } else {
            setupNormalText(meritText);
            isSpecialEffect = false;
        }

        ViewGroup root = binding.getRoot();
        root.addView(meritText);
        activeAnimationViews.add(meritText);

        // 强制测量TextView以获取正确的宽高
        measureTextView(meritText);

        // 使用post延迟计算位置和启动动画，确保测量完成
        meritText.post(() -> animateText(meritText, isSpecialEffect));
    }

    private void setupComboText(TextView textView) {
        // 在AndroidAutoSize项目中使用dp单位，让AutoSize自动处理屏幕适配
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, largeTextSizeDp);
        // 优化连击文字选择逻辑
        int textIndex = Math.min((comboCount - COMBO_THRESHOLD) / 2, COMBO_TEXTS.length - 1);
        textView.setText(COMBO_TEXTS[textIndex]);
        textView.setTextColor(COLOR_COMBO);
        // 阴影效果也使用dp单位，让AutoSize自动适配
        textView.setShadowLayer(4f, 0, 0, adjustAlpha(COLOR_COMBO, 0.6f));
        // 连击文字使用更粗的字体
        textView.setTypeface(null, android.graphics.Typeface.BOLD);
    }

    private void setupSpecialText(TextView textView) {
        // 在AndroidAutoSize项目中使用dp单位，让AutoSize自动处理屏幕适配
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, largeTextSizeDp);
        textView.setText(SPECIAL_TEXTS[(int) (Math.random() * SPECIAL_TEXTS.length)]);
        textView.setTextColor(COLOR_SPECIAL);
        // 阴影效果也使用dp单位，让AutoSize自动适配
        textView.setShadowLayer(4f, 0, 0, adjustAlpha(COLOR_SPECIAL, 0.6f));
        // 特殊文字使用粗体
        textView.setTypeface(null, android.graphics.Typeface.BOLD);
    }

    private void setupNormalText(TextView textView) {
        // 在AndroidAutoSize项目中使用dp单位，让AutoSize自动处理屏幕适配
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, normalTextSizeDp);
        textView.setText(MERIT_TEXTS[(int) (Math.random() * MERIT_TEXTS.length)]);
        textView.setTextColor(COLOR_NORMAL);
        // 普通文字使用正常字体
        textView.setTypeface(null, android.graphics.Typeface.BOLD);
    }

    private void animateText(final TextView textView, final boolean isSpecialEffect) {
        // 计算位置
        final float screenWidth = binding.getRoot().getWidth();
        final float screenHeight = binding.getRoot().getHeight();
        final float woodenFishX = binding.dOnclick.getX();
        final float woodenFishY = binding.dOnclick.getY();
        final float woodenFishHeight = binding.dOnclick.getHeight();
        final float woodenFishCenterY = woodenFishY + woodenFishHeight / 2;

        // 获取TextView的实际宽高，如果为0则使用测量值
        int textWidth = textView.getWidth();
        int textHeight = textView.getHeight();

        if (textWidth == 0 || textHeight == 0) {
            // 如果宽高为0，使用测量值
            textWidth = textView.getMeasuredWidth();
            textHeight = textView.getMeasuredHeight();
        }

        // 如果测量值仍为0，强制重新测量
        if (textWidth == 0 || textHeight == 0) {
            textView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
            textWidth = textView.getMeasuredWidth();
            textHeight = textView.getMeasuredHeight();
        }

        float startX, startY;
        if (isSpecialEffect) {
            // 特效文字居中显示
            startX = screenWidth / 2;
            // 调整到更合适的位置
            startY = screenHeight * 0.45f;
        } else {
            // 普通文字在左侧区域，优化位置计算
            // 在AndroidAutoSize项目中，直接使用dp值，让AutoSize自动适配
            float leftPadding = 30f; // dp值，AutoSize会自动转换
            // 调整水平位置
            startX = leftPadding + woodenFishX * 0.3f;

            // 优化垂直位置计算 - 增加更多位置变化
            float[] yOffsets = {-60, -30, 0, 30, 60, 90}; // dp值，增加更多位置
            lastPosition = (lastPosition + 1) % yOffsets.length;
            float offset = yOffsets[lastPosition]; // 直接使用dp值
            startY = woodenFishCenterY + offset;

            // 确保不超出边界，使用实际的文字高度
            float minY = textHeight / 2f;
            float maxY = screenHeight - textHeight / 2f;
            startY = Math.max(minY, Math.min(startY, maxY));
        }

        // 设置位置，使用实际的文字宽高进行居中
        float finalX = startX - (float) textWidth / 2;
        float finalY = startY - (float) textHeight / 2;

        // 确保X坐标也不超出屏幕边界
        float minX = 0;
        float maxX = screenWidth - textWidth;
        finalX = Math.max(minX, Math.min(finalX, maxX));

        textView.setX(finalX);
        textView.setY(finalY);

        // 创建并启动动画
        AnimatorSet animSet = createAnimatorSet(textView, isSpecialEffect);
        animSet.start();
    }

    private AnimatorSet createAnimatorSet(TextView textView, boolean isSpecialEffect) {
        AnimatorSet animSet = new AnimatorSet();

        if (isSpecialEffect) {
            // 优化特效动画
            PropertyValuesHolder pvhAlpha = PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f);
            PropertyValuesHolder pvhScaleX = PropertyValuesHolder.ofFloat(View.SCALE_X, 0.8f, 1.2f, 1f);
            PropertyValuesHolder pvhScaleY = PropertyValuesHolder.ofFloat(View.SCALE_Y, 0.8f, 1.2f, 1f);

            ObjectAnimator popIn = ObjectAnimator.ofPropertyValuesHolder(textView, pvhAlpha, pvhScaleX, pvhScaleY);
            popIn.setDuration(300);

            // 动态调整发光效果 - 在AndroidAutoSize项目中直接使用dp值
            ValueAnimator glowAnimator = ValueAnimator.ofFloat(4f, comboCount >= COMBO_THRESHOLD ? 10f : 7f);
            glowAnimator.setDuration(300);
            glowAnimator.addUpdateListener(animation -> {
                // 直接使用dp值，让AndroidAutoSize自动处理
                float blurRadius = (Float) animation.getAnimatedValue();
                int textColor = textView.getCurrentTextColor();
                textView.setShadowLayer(blurRadius, 0, 0, adjustAlpha(textColor, 0.7f));
            });

            ObjectAnimator stay = ObjectAnimator.ofFloat(textView, View.ALPHA, 1f);
            stay.setDuration(comboCount >= 5 ? 800 : 600);

            ObjectAnimator fadeOut = ObjectAnimator.ofFloat(textView, View.ALPHA, 1f, 0f);
            fadeOut.setDuration(350);

            // 创建一个新的AnimatorSet来组合popIn和glowAnimator
            AnimatorSet popInWithGlow = new AnimatorSet();
            popInWithGlow.playTogether(popIn, glowAnimator);

            // 按顺序播放动画
            animSet.playSequentially(
                    popInWithGlow,
                    stay,
                    fadeOut
            );
            animSet.setInterpolator(new DecelerateInterpolator(1.5f));
        } else {
            // 普通动画：简化动画属性，提高性能
            ObjectAnimator fadeIn = ObjectAnimator.ofFloat(textView, View.ALPHA, 0f, 1f);
            fadeIn.setDuration(150);

            ObjectAnimator stay = ObjectAnimator.ofFloat(textView, View.ALPHA, 1f);
            stay.setDuration(500);

            ObjectAnimator fadeOut = ObjectAnimator.ofFloat(textView, View.ALPHA, 1f, 0f);
            fadeOut.setDuration(250);

            animSet.playSequentially(fadeIn, stay, fadeOut);
            animSet.setInterpolator(normalInterpolator);
        }

        animSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                cleanupTextView(textView);
            }
        });

        return animSet;
    }

    private void cleanupTextView(TextView textView) {
        ((ViewGroup) textView.getParent()).removeView(textView);
        activeAnimationViews.remove(textView);
        textView.setTranslationY(0f);
        textView.setScaleX(1f);
        textView.setScaleY(1f);
        textView.setShadowLayer(0, 0, 0, 0);
        textViewPool.offer(textView);
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 清理资源
        textViewPool.clear();
        activeAnimationViews.clear();
        if (soundPool != null) {
            soundPool.release();
            soundPool = null;
        }
        if (animatorSet != null) {
            animatorSet.cancel();
            animatorSet = null;
        }
    }

    /**
     * 安全测量TextView，确保获取正确的宽高
     */
    private void measureTextView(TextView textView) {
        // 获取屏幕宽高作为最大约束
        int screenWidth = binding.getRoot().getWidth();
        int screenHeight = binding.getRoot().getHeight();

        // 如果屏幕尺寸还未确定，使用默认值
        if (screenWidth == 0) screenWidth = 1000; // 默认宽度
        if (screenHeight == 0) screenHeight = 600; // 默认高度

        // 创建测量规格，给TextView足够的空间
        int widthMeasureSpec = View.MeasureSpec.makeMeasureSpec(screenWidth, View.MeasureSpec.AT_MOST);
        int heightMeasureSpec = View.MeasureSpec.makeMeasureSpec(screenHeight, View.MeasureSpec.AT_MOST);

        // 执行测量
        textView.measure(widthMeasureSpec, heightMeasureSpec);
    }

    // 添加一个用于调整颜色透明度的辅助方法
    private int adjustAlpha(int color, float factor) {
        int alpha = Math.round(Color.alpha(color) * factor);
        int red = Color.red(color);
        int green = Color.green(color);
        int blue = Color.blue(color);
        return Color.argb(alpha, red, green, blue);
    }
}
